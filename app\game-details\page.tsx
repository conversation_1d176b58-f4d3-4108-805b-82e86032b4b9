"use client";
import React, { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { Tournament, TournamentCategory } from "@/app/types/CommonComponent.types";
import { Game, Lobby } from "@/app/types/Game";
import withSidebar from "@/app/hoc/withSidebar";
import TournamentSection from "@/app/components/tournamentSection";
import api from "@/app/utils/axiosInstance";
import { API_ENDPOINTS } from "@/app/constants/apiEndpoints";
import Loader from "@/app/components/common/Loader";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

const GameDetailsPage = () => {
    const searchParams = useSearchParams();
    const router = useRouter();
    const user = useSelector((state: RootState) => state.user);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [tournaments, setTournaments] = useState<Tournament[]>([]);
    const [game, setGame] = useState<Game | null>(null);
    const [lobbies, setLobbies] = useState<Lobby[]>([]);
    const [joiningLobby, setJoiningLobby] = useState<string | null>(null);

    const gameName = searchParams.get("game");
    const type = searchParams.get("type");
    const gameId = searchParams.get("game_id");

    useEffect(() => {
        if (gameId) {
            // If game_id is provided, fetch game details from the new API
            fetchGameDetails();
            fetchGameLobbies();
        } else if (gameName && type) {
            // If game and type are provided, fetch tournaments (legacy behavior)
            fetchTournaments();
        } else {
            router.push("/");
            return;
        }
    }, [gameName, type, gameId]);

    const fetchTournaments = async () => {
        try {
            setIsLoading(true);
            setError(null);

            const response = await api.get<{ data: TournamentCategory[] }>(API_ENDPOINTS.GET_ALL_TOURNAMENTS);

            if (response.status === 200) {
                const allCategories = response.data.data;
                // Find the category that matches the type
                const category = allCategories.find(cat => cat.type === type);

                if (category) {
                    // Filter tournaments by game name
                    const filteredTournaments = category.tournaments.filter(
                        tournament => tournament.name === gameName
                    );
                    setTournaments(filteredTournaments);
                } else {
                    setError("Category not found");
                }
            }
        } catch (error) {
            setError(error instanceof Error ? error.message : "An error occurred");
        } finally {
            setIsLoading(false);
        }
    };

    const fetchGameDetails = async () => {
        try {
            setIsLoading(true);
            setError(null);
            const response = await api.get(API_ENDPOINTS.GET_GAME_DETAILS(gameId || ""));
            if (response.status === 200) {
                setGame(response.data);
            }
        } catch (error) {
            console.error("Error fetching game details:", error);
            setError("Failed to load game details");
        } finally {
            setIsLoading(false);
        }
    };

    const fetchGameLobbies = async () => {
        try {
            const response = await api.get(API_ENDPOINTS.GET_GAME_LOBBIES(gameId || ""));
            if (response.status === 200) {
                // Use the actual API response data
                const apiLobbies = response.data || [];
                // Map API response to match our lobby structure and add default status
                const formattedLobbies = apiLobbies.map((lobby: any) => ({
                    ...lobby,
                    status: lobby.status || "open" as const, // Default to open if status not provided
                    max_players: lobby.max_players || 2, // Default values if not provided
                    current_players: lobby.current_players || 1
                }));
                setLobbies(formattedLobbies);
            }
        } catch (error) {
            console.error("Error fetching game lobbies:", error);
            setError("Failed to load game lobbies");
            setLobbies([]); // Set empty array on error
        }
    };

    const handleBack = () => {
        router.push("/#games");
    };

    const handleJoinLobby = async (lobbyId: string, lobbyPrice: number) => {
        // Check if user is logged in
        if (!user?.name) {
            alert("Please log in to join games");
            return;
        }

        // Check if user has access token
        const accessToken = document.cookie.split('; ').find(row => row.startsWith('access_token='));
        if (!accessToken) {
            alert("Please log in again. Authentication token not found.");
            return;
        }

        // Prevent multiple requests for the same lobby
        if (joiningLobby === lobbyId) {
            return;
        }

        try {
            setJoiningLobby(lobbyId);

            // Validate required data before making API call
            if (!gameId) {
                throw new Error("Game ID is missing");
            }
            if (!lobbyId) {
                throw new Error("Lobby ID is missing");
            }
            if (!game?.game_name) {
                throw new Error("Game name is missing");
            }

            console.log("Joining lobby:", {
                lobbyId,
                gameId,
                lobbyPrice,
                username: user.name
            });

            const requestData = {
                game_id: gameId,
                lobby_id: lobbyId,
                game_name: game?.game_name || "Tic-Tac-Toe",
                return_to: "https://gamyday.com"
            };

            console.log("=== REQUEST BODY ANALYSIS ===");
            console.log("Request Data:", JSON.stringify(requestData, null, 2));
            console.log("game_id:", gameId);
            console.log("lobby_id:", lobbyId);
            console.log("game_name:", game?.game_name);
            console.log("return_to:", `${window.location.origin}/game-play`);
            console.log("All required fields present:", {
                game_id: !!gameId,
                lobby_id: !!lobbyId,
                game_name: !!(game?.game_name),
                return_to: true
            });
            console.log("Note: amount and username are not part of booking API - handled by backend");

            console.log("Sending API request:", requestData);
            console.log("API Endpoint:", API_ENDPOINTS.CREATE_GAME_BOOKING);
            console.log("Full URL:", `${process.env.NEXT_PUBLIC_API_URL}/api${API_ENDPOINTS.CREATE_GAME_BOOKING}`);

            // Check if user is authenticated
            const accessToken = document.cookie.split('; ').find(row => row.startsWith('access_token='));
            console.log("Access token exists:", !!accessToken);


            console.log("Attempting to create booking...");

            try {
                // First attempt with the current API
                const response = await api.post(API_ENDPOINTS.CREATE_GAME_BOOKING, requestData);
                console.log("Booking API success:", response);

                if (response.status === 200 || response.status === 201) {
                    const bookingData = response.data;

                    console.log("Booking created successfully:", bookingData);

                    const expires = new Date(Date.now() + 1 * 60 * 60 * 1000).toUTCString();
                    document.cookie = `session_token=${encodeURIComponent(bookingData.session_token)}; path=/; Domain=.gamyday.com; Secure=true; expires=${expires}; SameSite=Lax`;
                    window.location.href = bookingData.redirect_url
                    console.log("Booking created successfully:", bookingData);
                    return; // Exit successfully
                } else {
                    throw new Error(`API returned status ${response.status}`);
                }
            } catch (primaryError: any) {
                console.log("Primary API call failed:", primaryError.message || primaryError);

                // Try with direct fetch (in case axios has configuration issues)
                try {
                    console.log("Trying direct fetch to correct endpoint...");
                    const directResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/games/bookings`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${accessToken?.split('=')[1]}`, // Extract token from cookie
                        },
                        body: JSON.stringify(requestData)
                    });

                    console.log("Direct fetch response status:", directResponse.status);
                    console.log("Direct fetch response ok:", directResponse.ok);

                    if (directResponse.ok) {
                        console.log("Direct fetch worked!");
                        const directData = await directResponse.json();
                        console.log("Direct fetch response data:", directData);

                        const expires = new Date(Date.now() + 1 * 60 * 60 * 1000).toUTCString();
                        document.cookie = `session_token=${encodeURIComponent(directData.session_token)}; path=/; Domain=.gamyday.com; Secure=true; expires=${expires}; SameSite=Lax`;
                        window.location.href = directData.redirect_url
                        return;
                    } else {
                        const errorText = await directResponse.text();
                        console.log("Direct fetch error response:", errorText);
                        throw new Error(`Direct fetch failed with status ${directResponse.status}: ${errorText}`);
                    }
                } catch (directError: any) {
                    console.log("Direct fetch also failed:", directError);
                }

                // If all API attempts fail, create a mock booking for development
                console.log("All API attempts failed, creating mock booking for development...");

                // Create a fallback booking for development purposes
                const mockBookingId = `mock_booking_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                console.log("Created mock booking ID:", mockBookingId);

                // Store booking info in localStorage for the game-play page
                const bookingInfo = {
                    bookingId: mockBookingId,
                    gameId: gameId,
                    lobbyId: lobbyId,
                    username: user.name,
                    amount: lobbyPrice,
                    created: new Date().toISOString()
                };

                localStorage.setItem(`booking_${mockBookingId}`, JSON.stringify(bookingInfo));

                // Show a warning that this is a mock booking
                alert("⚠️ Development Mode: Created mock booking. API connection failed.");

                // Proceed with the flow as if booking was successful
                if (lobbyPrice === 0) {
                    router.push(`/game-play?sessionId=${mockBookingId}&gameId=${gameId}&username=${encodeURIComponent(user.name)}`);
                } else {
                    router.push(`/my-wallet?booking_id=${mockBookingId}&lobby_id=${lobbyId}&amount=${lobbyPrice}&game_id=${gameId}&type=game&username=${encodeURIComponent(user.name)}`);
                }
                return;
            }

        } catch (error: any) {
            console.log("=== ERROR DEBUG ===");
            console.error("Full error object:", error);
            console.error("Error type:", typeof error);
            console.error("Error name:", error?.name);
            console.error("Error message:", error?.message);
            console.error("Error code:", error?.code);
            console.error("Error response:", error?.response);
            console.error("Error status:", error?.response?.status);
            console.error("Error data:", error?.response?.data);
            console.error("Error headers:", error?.response?.headers);
            console.error("Error config:", error?.config);
            console.error("Error request:", error?.request);

            // Additional network-level debugging
            console.log("=== NETWORK DEBUG ===");
            console.log("Error has response:", !!error?.response);
            console.log("Error has request:", !!error?.request);
            console.log("Is network error:", !error?.response && !!error?.request);
            console.log("Error stack:", error?.stack);

            // Better error handling
            let errorMessage = "Unknown error";

            if (error?.response?.data?.message) {
                errorMessage = error.response.data.message;
            } else if (error?.response?.data?.errors) {
                const errors = Object.values(error.response.data.errors).flat();
                errorMessage = errors.join(", ");
            } else if (error?.response?.status === 301 || error?.response?.status === 302) {
                // Handle redirect - the server wants us to use a different endpoint
                const redirectUrl = error.response.headers?.location;
                console.log("API redirected to:", redirectUrl);
                errorMessage = `API endpoint redirected to: ${redirectUrl}`;
            } else if (error?.response?.status === 401) {
                errorMessage = "Authentication required. Please log in again.";
            } else if (error?.response?.status === 403) {
                errorMessage = "Access denied. Check your permissions.";
            } else if (error?.response?.status === 404) {
                errorMessage = "API endpoint not found.";
            } else if (error?.response?.status >= 500) {
                errorMessage = "Server error. Please try again later.";
            } else if (error?.code === 'NETWORK_ERROR' || error?.code === 'ERR_NETWORK') {
                errorMessage = `Network error: ${error.message}. Check your connection and API URL.`;
            } else if (!error?.response && error?.request) {
                // Network error - request was made but no response received
                errorMessage = `Network error: No response from server at ${process.env.NEXT_PUBLIC_API_URL}. Check if the API is running and accessible.`;
            } else if (error?.message) {
                errorMessage = error.message;
            } else if (!error?.response) {
                errorMessage = "Network error. Check your connection.";
            }

            alert(`Failed to join lobby: ${errorMessage}`);
        }

        setJoiningLobby(null);
    };

    if (error) {
        return <div className="p-8 text-red-500">Error: {error}</div>;
    }

    // Render game details from new API
    if (gameId && game) {
        return (
            <div className="px-8 py-5">
                <div className="flex items-center gap-4 mb-6">
                    <button
                        onClick={handleBack}
                        className="bg-[#141517] text-white px-4 py-2 rounded-md font-medium hover:bg-opacity-80 transition-all border border-[#2a2c2e] flex items-center space-x-2"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                        </svg>
                        <span>Back to All Games</span>
                    </button>
                    <h1 className="text-3xl font-bold text-white">{game.game_name}</h1>
                </div>

                {isLoading ? (
                    <div className="flex justify-center items-center h-[50vh]">
                        <Loader />
                    </div>
                ) : (
                    <div className="relative bg-[#141517] rounded-[30px] border border-[#707070] text-white w-full mb-6 p-6">
                        <div className="flex">
                            {/* Left section - Game Image */}
                            <div className="flex-shrink-0 w-[330px] flex flex-col justify-center items-center">
                                <div className="relative mb-2">
                                    <img
                                        src={game.image || `/icons/Tic-Tac-Toe.png`}
                                        alt={game.game_name}
                                        className="w-[320px] h-[320px] rounded-lg object-cover border-2 border-[#c9ff88]"
                                        onError={(e) => {
                                            const target = e.target as HTMLImageElement;
                                            target.src = '/icons/twogames.png'; // Fallback image
                                        }}
                                    />
                                </div>
                                
                                {/* Game description below image */}
                                <div className="mt-4 w-full bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e]">
                                    <h3 className="text-xl font-bold text-white mb-2">{game.game_name}</h3>
                                    <p className="text-gray-300 text-sm">{game.game_desc}</p>
                                </div>
                            </div>

                            {/* Right section - Lobbies */}
                            <div className="flex-1 ml-6">
                                <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e] mb-4">
                                    <div className="flex items-center mb-2">
                                        <div className="w-2 h-5 bg-[#c9ff88] rounded-sm mr-2"></div>
                                        <h2 className="text-xl font-bold text-white">Available Lobbies</h2>
                                    </div>
                                    
                                    {lobbies.length > 0 ? (
                                        <div className="max-h-[400px] overflow-y-auto pr-2 scrollbar-hide">
                                            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                                                {lobbies.map((lobby) => (
                                                    <div key={lobby.id} className="flex flex-col mb-3 pb-3 border border-[#2a2c2e] rounded-md p-3 bg-[#141517]">
                                                        <h3 className="text-lg font-semibold text-white mb-2">{lobby.lobby_name}</h3>
                                                        <p className="text-gray-400 mb-3 text-sm">{lobby.lobby_desc}</p>
                                                        <div className="flex justify-between items-center text-sm text-gray-400 mb-4">
                                                            {lobby.current_players && lobby.max_players ? (
                                                                <span>Players: {lobby.current_players}/{lobby.max_players}</span>
                                                            ) : (
                                                                <span>Price: ₹{lobby.lobby_price}</span>
                                                            )}
                                                            {lobby.status && (
                                                                <span className={`px-2 py-1 rounded ${lobby.status === 'open' || lobby.status === 'active' ? 'bg-green-900 text-green-300' :
                                                                    lobby.status === 'in_progress' ? 'bg-yellow-900 text-yellow-300' :
                                                                        'bg-red-900 text-red-300'
                                                                    }`}>
                                                                    {lobby.status === 'open' || lobby.status === 'active' ? 'Open' :
                                                                        lobby.status === 'in_progress' ? 'In Progress' :
                                                                            'Closed'}
                                                                </span>
                                                            )}
                                                        </div>
                                                        <button
                                                            className={`w-full py-2 rounded-md font-semibold ${joiningLobby === lobby.id ? 'bg-gray-600 text-gray-400 cursor-not-allowed' :
                                                                (!lobby.status || lobby.status === 'open' || lobby.status === 'active') ? 'bg-[#c9ff88] text-[#070b28] hover:bg-opacity-90' : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                                                                }`}
                                                            onClick={() => handleJoinLobby(lobby.id, lobby.lobby_price)}
                                                            disabled={lobby.status === 'closed' || lobby.status === 'full' || joiningLobby === lobby.id}
                                                        >
                                                            {joiningLobby === lobby.id ? 'Joining...' :
                                                                (!lobby.status || lobby.status === 'open' || lobby.status === 'active') ? `Join Lobby (₹${lobby.lobby_price})` : 'Lobby Closed'}
                                                        </button>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="text-center p-6 bg-[#141517] rounded-lg border border-[#2a2c2e]">
                                            <p className="text-gray-400">No lobbies available for this game at the moment.</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    }

    // Render tournament-based game details (legacy behavior)
    return (
        <div className="px-8 py-5">
            <div className="flex items-center gap-4 mb-6">
                <button
                    onClick={handleBack}
                    className="bg-[#141517] text-white px-4 py-2 rounded-md font-medium hover:bg-opacity-80 transition-all border border-[#2a2c2e] flex items-center space-x-2"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                    </svg>
                    <span>Back to All Games</span>
                </button>
                <h1 className="text-3xl font-bold text-white">{game?.game_name || 'Game Details'}</h1>
            </div>

            {isLoading ? (
                <div className="flex justify-center items-center h-[50vh]">
                    <Loader />
                </div>
            ) : tournaments.length > 0 ? (
                <TournamentSection
                    tournaments={tournaments}
                    sectionTitle=""
                    selectedTournamentId={null}
                />
            ) : (
                <div className="relative bg-[#141517] rounded-[30px] border border-[#707070] text-white w-full mb-6 p-6">
                    <div className="flex">
                        {/* Left section - Game Image */}
                        <div className="flex-shrink-0 w-[200px] flex flex-col justify-center items-center">
                            <div className="relative mb-2">
                                <img
                                    src={game?.image || `/icons/twogames.png`}
                                    alt={game?.game_name || 'Game'}
                                    className="w-[180px] h-[180px] rounded-lg object-cover border-2 border-[#c9ff88]"
                                    onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.src = '/icons/twogames.png'; // Fallback image
                                    }}
                                />
                            </div>
                            
                            {/* Game description below image */}
                            <div className="mt-4 w-full bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e]">
                                <h3 className="text-xl font-bold text-white mb-2">{game?.game_name || 'Game Details'}</h3>
                                <p className="text-gray-300 text-sm">{game?.game_desc || 'No description available'}</p>
                                
                                <button className="mt-4 w-full bg-[#c9ff88] text-[#070b28] px-4 py-2 rounded-md font-semibold hover:bg-opacity-90 transition-all duration-300">
                                    Play Now
                                </button>
                            </div>
                        </div>

                        {/* Right section - Lobbies */}
                        <div className="flex-1 ml-6">
                            <div className="bg-[#1a1c1e] rounded-lg p-3 border border-[#2a2c2e] mb-4">
                                <div className="flex items-center mb-2">
                                    <div className="w-2 h-5 bg-[#c9ff88] rounded-sm mr-2"></div>
                                    <h2 className="text-xl font-bold text-white">Available Lobbies</h2>
                                </div>
                                
                                {lobbies.length > 0 ? (
                                    <div className="max-h-[400px] overflow-y-auto pr-2 scrollbar-hide">
                                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                                            {lobbies.map((lobby) => (
                                                <div key={lobby.id} className="flex flex-col mb-3 pb-3 border border-[#2a2c2e] rounded-md p-3 bg-[#141517]">
                                                    <h3 className="text-lg font-semibold text-white mb-2">{lobby.lobby_name}</h3>
                                                    <p className="text-gray-400 mb-3 text-sm">{lobby.lobby_desc}</p>
                                                    <div className="flex justify-between items-center text-sm text-gray-400 mb-4">
                                                        {lobby.current_players && lobby.max_players ? (
                                                            <span>Players: {lobby.current_players}/{lobby.max_players}</span>
                                                        ) : (
                                                            <span>Price: ₹{lobby.lobby_price}</span>
                                                        )}
                                                        {lobby.status && (
                                                            <span className={`px-2 py-1 rounded ${lobby.status === 'open' || lobby.status === 'active' ? 'bg-green-900 text-green-300' :
                                                                lobby.status === 'in_progress' ? 'bg-yellow-900 text-yellow-300' :
                                                                    'bg-red-900 text-red-300'
                                                                }`}>
                                                                {lobby.status === 'open' || lobby.status === 'active' ? 'Open' :
                                                                    lobby.status === 'in_progress' ? 'In Progress' :
                                                                        'Closed'}
                                                            </span>
                                                        )}
                                                    </div>
                                                    <button
                                                        className={`w-full py-2 rounded-md font-semibold ${(!lobby.status || lobby.status === 'open' || lobby.status === 'active') ? 'bg-[#c9ff88] text-[#070b28] hover:bg-opacity-90' : 'bg-gray-600 text-gray-400 cursor-not-allowed'}`}
                                                        onClick={() => handleJoinLobby(lobby.id, lobby.lobby_price)}
                                                        disabled={lobby.status === 'closed' || lobby.status === 'full'}
                                                    >
                                                        {(!lobby.status || lobby.status === 'open' || lobby.status === 'active') ? `Join Lobby (₹${lobby.lobby_price})` : 'Lobby Closed'}
                                                    </button>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                ) : (
                                    <div className="text-center p-6 bg-[#141517] rounded-lg border border-[#2a2c2e]">
                                        <p className="text-gray-400">No lobbies available for this game at the moment.</p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default withSidebar(GameDetailsPage);
